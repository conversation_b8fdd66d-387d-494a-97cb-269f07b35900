import { NetworkStatus, useReactiveVar } from '@apollo/client';
import { useContext, useMemo } from 'react';
import { useFormattedDate } from 'shared/components/date-range/common';
import { LocizeFinanceKeys } from 'shared/constants/localization-keys';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';
import { useMerchantPermissionBits } from 'shared/hooks/user/permissions';
import { useFormattedAmount } from 'shared/hooks/utils';
import { MerchantPermissions } from 'shared/types';
import { hasPermissions } from 'shared/utils';

import { useOverdueBanner } from '../components/OverdueBanner.hooks';
import { transactionsTableState } from '../transactions-table/TransactionsTable.utils';
import { useMerchantBalanceQuery, useMerchantRecentInvoiceQuery } from './api';

type CardData = {
  title: string;
  isIncreased: boolean;
  balance: string;
  description: string;
  isVisible?: boolean;
};

export const useMerchantBalanceCardsData = () => {
  const { merchantId } = useContext(GlobalStateContext);
  const { dateRange } = useReactiveVar(transactionsTableState);
  const {
    networkStatus: merchantDetailsQueryNetworkStatus,
    data: merchantData,
  } = useMerchantDetails({
    notifyOnNetworkStatusChange: true,
  });
  const dateRangeTo = useFormattedDate(
    dateRange.to ? dateRange.to : new Date(),
  );

  const { data, networkStatus: merchantBalanceQueryNetworkStatus } =
    useMerchantBalanceQuery({
      notifyOnNetworkStatusChange: true,
      skip: !merchantId,
      variables: {
        merchantId: merchantId ?? -1,
        balanceAt: dateRangeTo,
      },
    });

  const principalBalanceFormatted = useFormattedAmount(
    data?.balance?.pending_principal || 0,
  );
  const bonusBalanceFormatted = useFormattedAmount(
    data?.balance?.pending_bonus || 0,
  );

  const unpaidAmountFormatted = useFormattedAmount(
    data?.balance?.unpaid_amount || 0,
  );

  console.log('unpaidAmountFormatted', unpaidAmountFormatted);

  const balanceCardsData: Array<CardData> = useMemo(
    () => [
      {
        title: LocizeFinanceKeys.BALANCE_TITLE,
        isIncreased:
          !!data?.balance?.pending_principal &&
          data?.balance?.pending_principal > 0,
        balance: principalBalanceFormatted,
        description: LocizeFinanceKeys.BALANCE_DESCRIPTION,
      },
      {
        title: LocizeFinanceKeys.BONUS_TITLE,
        isIncreased:
          !!data?.balance?.pending_bonus && data?.balance?.pending_bonus > 0,
        balance: bonusBalanceFormatted,
        description: LocizeFinanceKeys.BONUS_DESCRIPTION,
        isVisible:
          !!merchantData?.merchant?.settings?.bonus_pct &&
          merchantData?.merchant?.settings?.bonus_pct > 0,
      },
      {
        title: LocizeFinanceKeys.UNPAID_AMOUNT_TITLE,
        isIncreased:
          !!data?.balance?.unpaid_amount && data?.balance?.unpaid_amount > 0,
        balance: unpaidAmountFormatted,
        description: LocizeFinanceKeys.UNPAID_AMOUNT_DESCRIPTION,
      },
    ],
    [
      data?.balance?.pending_principal,
      data?.balance?.pending_bonus,
      data?.balance?.unpaid_amount,
      principalBalanceFormatted,
      bonusBalanceFormatted,
      unpaidAmountFormatted,
      merchantData?.merchant?.settings?.bonus_pct,
    ],
  );

  return {
    balanceCardsData,
    isFetched:
      merchantBalanceQueryNetworkStatus === NetworkStatus.ready &&
      merchantDetailsQueryNetworkStatus === NetworkStatus.ready,
  };
};

export const useMerchantOverdueBanner = () => {
  const { merchantId } = useContext(GlobalStateContext);
  const merchantPermissionBits = useMerchantPermissionBits();

  const hasAdminPermission =
    !!merchantPermissionBits &&
    hasPermissions(merchantPermissionBits, [MerchantPermissions.Admin]);

  const shouldSkipQueries = !merchantId || !hasAdminPermission;

  const { data: balanceData } = useMerchantBalanceQuery({
    skip: shouldSkipQueries,
    variables: {
      merchantId: merchantId ?? -1,
    },
  });

  const { data: recentInvoiceData } = useMerchantRecentInvoiceQuery({
    skip: shouldSkipQueries,
    variables: {
      merchantId: merchantId ?? -1,
    },
  });

  const unpaidAmount = balanceData?.balance?.unpaid_amount || 0;
  const recentInvoice = recentInvoiceData?.invoices?.data?.[0];
  const dueDate = recentInvoice?.due_at;

  const bannerData = useOverdueBanner({
    unpaidAmount,
    dueDate,
  });

  // Return null if user doesn't have Admin permissions
  if (!hasAdminPermission) {
    return null;
  }

  return bannerData;
};
